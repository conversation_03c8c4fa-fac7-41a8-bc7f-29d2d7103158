import { UserProfile } from '../../profile/models';

export class ReleaseNotes {
    id: string;
    createdBy: string;
    notes: string;
    createdAt: Date;

    constructor(options?: Partial<ReleaseNotes>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                this[key] = value;
            }
        }
    }

    static create(user: UserProfile, contents: string) {
        return new ReleaseNotes({
            createdBy: user.id,
            createdAt: new Date(),
            notes: contents,
        });
    }
}
