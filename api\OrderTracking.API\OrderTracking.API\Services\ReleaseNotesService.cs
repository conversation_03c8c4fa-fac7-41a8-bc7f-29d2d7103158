using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Azure Cosmos DB-based service class to CRUD release notes documents (migrated from Firestore)
    /// </summary>
    public class ReleaseNotesService : IReleaseNotesService
    {
        // Migrated from Firestore CollectionReference to Azure Cosmos DB Container
        private readonly Container _container;

        /// <summary>
        ///     Constructor for the class
        /// </summary>
        /// <param name="cosmosClientAdapter">Azure Cosmos DB client adapter</param>
        /// <param name="options">Connection options</param>
        public ReleaseNotesService(ICosmosClientAdapter cosmosClientAdapter, IOptions<Connections> options)
        {
            if (cosmosClientAdapter == null) throw new ArgumentNullException(nameof(cosmosClientAdapter));
            if (options == null) throw new ArgumentNullException(nameof(options));

            // Get the container for release notes
            var cosmosClient = cosmosClientAdapter.GetClient();
            var database = cosmosClient.GetDatabase(options.Value.Database ?? "OrderTrackingDB");
            _container = database.GetContainer(options.Value.ReleaseNotes ?? "release-notes");
        }

        /// <summary>
        ///     Get items ordered by createdAt, desc
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ReleaseNotes>> GetItemsAsync()
        {
            // Migrated from Firestore to Azure Cosmos DB
            var query = "SELECT * FROM c ORDER BY c.CreatedAt DESC";
            var queryDefinition = new QueryDefinition(query);
            var queryResultSetIterator = _container.GetItemQueryIterator<ReleaseNotes>(queryDefinition);

            var results = new List<ReleaseNotes>();
            while (queryResultSetIterator.HasMoreResults)
            {
                var currentResultSet = await queryResultSetIterator.ReadNextAsync();
                results.AddRange(currentResultSet);
            }

            return results;
        }

        /// <summary>
        ///     Create a release notes document in the database.
        /// </summary>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> AddItemAsync(ReleaseNotes releaseNotes)
        {
            // Migrated from Firestore to Azure Cosmos DB
            // Generate a unique ID for the document
            var id = Guid.NewGuid().ToString();
            releaseNotes.Id = id;

            // Create the item in Cosmos DB
            var response = await _container.CreateItemAsync(releaseNotes, new PartitionKey(id));
            return response.Resource;
        }

        /// <summary>
        ///     Delete a release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id)
        {
            // Migrated from Firestore to Azure Cosmos DB
            await _container.DeleteItemAsync<ReleaseNotes>(id, new PartitionKey(id));
        }

        /// <summary>
        ///     Update an existing release notes document in the database.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, ReleaseNotes releaseNotes)
        {
            if (releaseNotes == null) throw new ArgumentNullException(nameof(releaseNotes));

            // Migrated from Firestore to Azure Cosmos DB
            // Ensure the ID is set in the object
            releaseNotes.Id = id;
            await _container.UpsertItemAsync(releaseNotes, new PartitionKey(id));
        }

        /// <summary>
        ///     Get a single release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> GetItemAsync(string id)
        {
            try
            {
                // Migrated from Firestore to Azure Cosmos DB
                var response = await _container.ReadItemAsync<ReleaseNotes>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                return default;
            }
        }

        /// <summary>
        ///     Delete multiple release notes items from the database at once.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                // Migrated from Firestore to Azure Cosmos DB
                await _container.DeleteItemAsync<ReleaseNotes>(id, new PartitionKey(id));
            }
        }

    }
}